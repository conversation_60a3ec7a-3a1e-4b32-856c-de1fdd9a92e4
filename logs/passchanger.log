Database initialized at data/passchanger.db
Generated new encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
LLM connection test failed: 'name'
Failed to initialize AI engine: 'name'
Failed to initialize components: 'name'
Fatal error: 'name'
Database connections closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: []
Model deepseek-r1:32b not found. Available models: []
LLM connection test failed: No models available in Ollama
Failed to initialize AI engine: No models available in Ollama
Failed to initialize components: No models available in Ollama
Fatal error: No models available in Ollama
Database connections closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7f14cf73ae40>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7f6694ecf1a0>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Added account: GeekDadKevin
Scanning 1 accounts for leaks
HIBP API error 401 for <EMAIL>
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
Recorded leak detection: Potential leak found in search: BreachDirectory - Check If Your Email or Username was Compromised
Recorded leak detection: Potential leak found in search: Data Breach Lookup | Check If Your Information Was Exposed
Recorded leak detection: Potential leak found in search: Has Your Email Been Compromised? Free Data Breach Checker - Norton
Recorded leak detection: Potential leak found in search: Check if Your Email Has Been Exposed in a Data Breach - WhatIsMyIPAddress
Recorded leak detection: Potential leak found in search: Have I Been Breached
Recorded leak detection: Potential leak found in search: Sign in - Google Accounts
Recorded leak detection: Potential leak found in search: Learn More About Google's Secure and Protected Accounts - Google
Leak scan completed. Found 7 potential issues
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7f4f4b6f9880>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Leak scan completed. Found 0 potential issues
Database connections closed
Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x7fca50fe6900>
Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x7fca50ece930>, 1925.*********)])', 'deque([(<aiohttp.client_proto.ResponseHandler object at 0x7fca50ecec30>, 1925.*********)])']
connector: <aiohttp.connector.TCPConnector object at 0x7fca4ef69ca0>
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Leak scan completed. Found 0 potential issues
Leak detector HTTP session closed
Leak detector closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Leak detector HTTP session closed
Leak detector closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Leak scan completed. Found 0 potential issues
Leak detector HTTP session closed
Leak detector closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Leak detector HTTP session closed
Leak detector closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Leak scan completed. Found 0 potential issues
Leak detector HTTP session closed
Leak detector closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: deepseek-r1:32b
Tor manager initialized
Tor session created successfully
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Starting darkweb scan for 1 accounts
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Darkweb scan completed. Found 0 potential issues
Leak scan completed. Found 0 potential issues
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['deepseek-r1:32b']
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['llama3.2:3b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: llama3.2:3b
Tor manager initialized
Tor session created successfully
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Starting darkweb scan for 1 accounts
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing http://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Could not connect to Tor controller: [Errno 111] Connection refused
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['llama3.2:3b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: llama3.2:3b
Tor manager initialized
Tor controller not available: [Errno 111] Connection refused
SOCKS proxy is running but control port isn't accessible
Tor session created successfully
Leak detector initialized
All components initialized successfully
Stopping Tor...
Tor stopped
Tor is already running
Leak detector HTTP session closed
Leak detector Tor session closed
Leak detector closed
Stopping Tor...
Tor stopped
Tor manager closed
Tor manager closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['llama3.2:3b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: llama3.2:3b
Tor manager initialized
Tor controller not available: [Errno 111] Connection refused
SOCKS proxy is running but control port isn't accessible
Tor session created successfully
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Starting darkweb scan for 1 accounts
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Darkweb scan completed. Found 0 potential issues
Leak scan completed. Found 0 potential issues
Leak detector HTTP session closed
Leak detector Tor session closed
Leak detector closed
Stopping Tor...
Tor stopped
Tor manager closed
Tor manager closed
Database connections closed
Database manager closed
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['llama3.2:3b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: llama3.2:3b
Tor manager initialized
Tor controller not available: [Errno 111] Connection refused
SOCKS proxy is running but control port isn't accessible
Tor session created successfully
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Starting darkweb scan for 1 accounts
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Database initialized at data/passchanger.db
Loaded existing encryption key
Security manager initialized
HTTP Request: GET http://127.0.0.1:11434/api/tags "HTTP/1.1 200 OK"
Available models: ['llama3.2:3b']
HTTP Request: POST http://127.0.0.1:11434/api/generate "HTTP/1.1 200 OK"
LLM connection test successful
AI Engine initialized with model: llama3.2:3b
Tor manager initialized
Tor controller not available: [Errno 111] Connection refused
SOCKS proxy is running but control port isn't accessible
Tor session created successfully
Leak detector initialized
All components initialized successfully
Scanning 1 accounts for leaks
Starting darkweb scan for 1 accounts
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://duckduckgogg42ts72.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
Error accessing https://3g2upl4pq6kufc4m.onion: General SOCKS server failure
